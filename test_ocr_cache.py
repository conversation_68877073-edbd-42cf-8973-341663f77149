#!/usr/bin/env python3
"""
Comprehensive Test Suite for OCR Cache System
Tests cache functionality, integration with OCR pipeline, and performance
"""

import unittest
import tempfile
import shutil
import time
import hashlib
from pathlib import Path
from unittest.mock import Mock, patch

# Import modules to test
try:
    from ocr_cache import OcrCache, initialize_cache, get_cache, shutdown_cache
    from config import ConfigManager, CacheConfig, AppConfig
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Error: Required modules not available: {e}")
    MODULES_AVAILABLE = False


class TestOcrCache(unittest.TestCase):
    """Test cases for OCR cache functionality"""
    
    def setUp(self):
        """Set up test environment"""
        if not MODULES_AVAILABLE:
            self.skipTest("Required modules not available")
        
        # Create temporary directory for test cache
        self.temp_dir = tempfile.mkdtemp()
        self.cache_dir = Path(self.temp_dir) / "test_cache"
        
        # Initialize test cache
        self.cache = OcrCache(
            cache_dir=str(self.cache_dir),
            cache_enabled=True,
            expiration_days=1,
            max_cache_size_mb=10
        )
        
        # Test data
        self.test_data_1 = b"This is test image data 1"
        self.test_data_2 = b"This is test image data 2"
        self.test_result_1 = "OCR result for image 1"
        self.test_result_2 = "OCR result for image 2"
    
    def tearDown(self):
        """Clean up test environment"""
        if hasattr(self, 'cache'):
            self.cache.close()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_initialization(self):
        """Test cache initialization"""
        self.assertTrue(self.cache.cache_enabled)
        self.assertTrue(self.cache_dir.exists())
        self.assertTrue((self.cache_dir / "ocr_cache.db").exists())
    
    def test_file_hashing(self):
        """Test file content hashing"""
        hash1 = self.cache._calculate_file_hash(self.test_data_1)
        hash2 = self.cache._calculate_file_hash(self.test_data_1)
        hash3 = self.cache._calculate_file_hash(self.test_data_2)
        
        # Same data should produce same hash
        self.assertEqual(hash1, hash2)
        
        # Different data should produce different hash
        self.assertNotEqual(hash1, hash3)
        
        # Hash should be SHA-256 (64 hex characters)
        self.assertEqual(len(hash1), 64)
        self.assertTrue(all(c in '0123456789abcdef' for c in hash1))
    
    def test_cache_store_and_lookup(self):
        """Test basic cache store and lookup operations"""
        # Store data in cache
        success = self.cache.store(self.test_data_1, self.test_result_1, "test1.jpg")
        self.assertTrue(success)
        
        # Lookup data from cache
        result = self.cache.lookup(self.test_data_1, "test1.jpg")
        self.assertEqual(result, self.test_result_1)
        
        # Lookup non-existent data
        result = self.cache.lookup(self.test_data_2, "test2.jpg")
        self.assertIsNone(result)
    
    def test_cache_statistics(self):
        """Test cache statistics tracking"""
        # Initial stats
        stats = self.cache.get_stats()
        self.assertEqual(stats.hits, 0)
        self.assertEqual(stats.misses, 0)
        self.assertEqual(stats.total_lookups, 0)
        
        # Store and lookup data
        self.cache.store(self.test_data_1, self.test_result_1, "test1.jpg")
        
        # Cache hit
        result = self.cache.lookup(self.test_data_1, "test1.jpg")
        self.assertEqual(result, self.test_result_1)
        
        # Cache miss
        result = self.cache.lookup(self.test_data_2, "test2.jpg")
        self.assertIsNone(result)
        
        # Check updated stats
        stats = self.cache.get_stats()
        self.assertEqual(stats.hits, 1)
        self.assertEqual(stats.misses, 1)
        self.assertEqual(stats.total_lookups, 2)
        self.assertEqual(stats.hit_ratio, 50.0)
    
    def test_cache_clear(self):
        """Test cache clearing functionality"""
        # Store some data
        self.cache.store(self.test_data_1, self.test_result_1, "test1.jpg")
        self.cache.store(self.test_data_2, self.test_result_2, "test2.jpg")
        
        # Verify data is stored
        result1 = self.cache.lookup(self.test_data_1, "test1.jpg")
        result2 = self.cache.lookup(self.test_data_2, "test2.jpg")
        self.assertEqual(result1, self.test_result_1)
        self.assertEqual(result2, self.test_result_2)
        
        # Clear cache
        success = self.cache.clear_cache()
        self.assertTrue(success)
        
        # Verify data is gone
        result1 = self.cache.lookup(self.test_data_1, "test1.jpg")
        result2 = self.cache.lookup(self.test_data_2, "test2.jpg")
        self.assertIsNone(result1)
        self.assertIsNone(result2)
    
    def test_cache_expiration(self):
        """Test cache entry expiration"""
        # Create cache with very short expiration
        short_cache = OcrCache(
            cache_dir=str(self.cache_dir / "short_expiry"),
            cache_enabled=True,
            expiration_days=0,  # Use 1 second expiration (handled in cache logic)
            max_cache_size_mb=10
        )

        try:
            # Store data
            success = short_cache.store(self.test_data_1, self.test_result_1, "test1.jpg")
            self.assertTrue(success)

            # Immediate lookup should work
            result = short_cache.lookup(self.test_data_1, "test1.jpg")
            self.assertEqual(result, self.test_result_1)

            # Wait longer than expiration threshold and lookup again - should be expired
            time.sleep(1.5)  # Wait longer than 1 second threshold
            result = short_cache.lookup(self.test_data_1, "test1.jpg")
            self.assertIsNone(result)

        finally:
            short_cache.close()
    
    def test_cache_disabled(self):
        """Test cache behavior when disabled"""
        disabled_cache = OcrCache(
            cache_dir=str(self.cache_dir / "disabled"),
            cache_enabled=False,
            expiration_days=1,
            max_cache_size_mb=10
        )
        
        try:
            # Store should fail
            success = disabled_cache.store(self.test_data_1, self.test_result_1, "test1.jpg")
            self.assertFalse(success)
            
            # Lookup should return None
            result = disabled_cache.lookup(self.test_data_1, "test1.jpg")
            self.assertIsNone(result)
            
        finally:
            disabled_cache.close()
    
    def test_empty_data_handling(self):
        """Test handling of empty or None data"""
        # Empty data
        result = self.cache.lookup(b"", "empty.jpg")
        self.assertIsNone(result)
        
        success = self.cache.store(b"", "empty result", "empty.jpg")
        self.assertFalse(success)
        
        # None data should not crash
        result = self.cache.lookup(None, "none.jpg")
        self.assertIsNone(result)
    
    def test_cache_info(self):
        """Test cache information retrieval"""
        info = self.cache.get_cache_info()
        
        self.assertTrue(info['enabled'])
        self.assertIn('database_path', info)
        self.assertIn('expiration_days', info)
        self.assertIn('statistics', info)
        
        stats = info['statistics']
        self.assertIn('total_entries', stats)
        self.assertIn('hit_ratio_percent', stats)


class TestConfigManager(unittest.TestCase):
    """Test cases for configuration management"""
    
    def setUp(self):
        """Set up test environment"""
        if not MODULES_AVAILABLE:
            self.skipTest("Required modules not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.json"
    
    def tearDown(self):
        """Clean up test environment"""
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_default_config(self):
        """Test default configuration values"""
        config_manager = ConfigManager(str(self.config_file))
        config = config_manager.get_config()
        
        self.assertIsInstance(config, AppConfig)
        self.assertIsInstance(config.cache, CacheConfig)
        self.assertTrue(config.cache.enabled)
        self.assertEqual(config.cache.cache_dir, ".cache")
        self.assertEqual(config.cache.expiration_days, 30)
    
    def test_config_file_persistence(self):
        """Test configuration file saving and loading"""
        config_manager = ConfigManager(str(self.config_file))
        
        # Modify configuration
        config_manager.update_cache_config(
            enabled=False,
            expiration_days=7,
            cache_dir="custom_cache"
        )
        
        # Save configuration
        config_manager.save_config()
        self.assertTrue(self.config_file.exists())
        
        # Load configuration in new manager
        new_config_manager = ConfigManager(str(self.config_file))
        new_config = new_config_manager.get_config()
        
        self.assertFalse(new_config.cache.enabled)
        self.assertEqual(new_config.cache.expiration_days, 7)
        self.assertEqual(new_config.cache.cache_dir, "custom_cache")


class TestPerformance(unittest.TestCase):
    """Performance tests for cache system"""
    
    def setUp(self):
        """Set up test environment"""
        if not MODULES_AVAILABLE:
            self.skipTest("Required modules not available")
        
        self.temp_dir = tempfile.mkdtemp()
        self.cache_dir = Path(self.temp_dir) / "perf_cache"
        
        self.cache = OcrCache(
            cache_dir=str(self.cache_dir),
            cache_enabled=True,
            expiration_days=30,
            max_cache_size_mb=100
        )
    
    def tearDown(self):
        """Clean up test environment"""
        if hasattr(self, 'cache'):
            self.cache.close()
        if hasattr(self, 'temp_dir'):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_cache_performance(self):
        """Test cache performance with multiple entries"""
        num_entries = 100
        test_data = []
        
        # Generate test data
        for i in range(num_entries):
            data = f"Test image data {i}".encode()
            result = f"OCR result {i}"
            test_data.append((data, result))
        
        # Measure store performance
        start_time = time.time()
        for data, result in test_data:
            self.cache.store(data, result, f"test_{len(data)}.jpg")
        store_time = time.time() - start_time
        
        # Measure lookup performance
        start_time = time.time()
        for data, expected_result in test_data:
            result = self.cache.lookup(data, f"test_{len(data)}.jpg")
            self.assertEqual(result, expected_result)
        lookup_time = time.time() - start_time
        
        # Performance assertions (should be fast)
        self.assertLess(store_time, 5.0, "Store operations too slow")
        self.assertLess(lookup_time, 2.0, "Lookup operations too slow")
        
        # Verify all entries are cached
        stats = self.cache.get_stats()
        self.assertEqual(stats.cache_size, num_entries)
        self.assertEqual(stats.hits, num_entries)
        self.assertEqual(stats.hit_ratio, 100.0)
        
        print(f"Performance test results:")
        print(f"  Store time for {num_entries} entries: {store_time:.3f}s")
        print(f"  Lookup time for {num_entries} entries: {lookup_time:.3f}s")
        print(f"  Average store time per entry: {store_time/num_entries*1000:.2f}ms")
        print(f"  Average lookup time per entry: {lookup_time/num_entries*1000:.2f}ms")


def run_integration_test():
    """Run integration test with mock OCR service"""
    print("\n🧪 Running OCR Cache Integration Test")
    print("=" * 50)
    
    try:
        # Initialize cache
        temp_dir = tempfile.mkdtemp()
        cache_dir = Path(temp_dir) / "integration_cache"
        
        cache = OcrCache(
            cache_dir=str(cache_dir),
            cache_enabled=True,
            expiration_days=30
        )
        
        # Mock OCR service
        class MockOcrService:
            def __init__(self):
                self.call_count = 0
            
            def extract_text_from_image(self, image_data):
                self.call_count += 1
                # Simulate OCR processing time
                time.sleep(0.1)
                return f"OCR result for image with hash {hashlib.sha256(image_data).hexdigest()[:8]}"
        
        ocr_service = MockOcrService()
        
        # Test data
        test_images = [
            b"Image data 1",
            b"Image data 2",
            b"Image data 1",  # Duplicate
            b"Image data 3",
            b"Image data 2",  # Duplicate
        ]
        
        results = []
        
        # Process images with cache
        for i, image_data in enumerate(test_images):
            print(f"Processing image {i+1}...")
            
            # Check cache first
            cached_result = cache.lookup(image_data, f"image_{i+1}.jpg")
            
            if cached_result:
                print(f"  ✅ Cache hit - using cached result")
                result = cached_result
            else:
                print(f"  🔄 Cache miss - performing OCR")
                result = ocr_service.extract_text_from_image(image_data)
                cache.store(image_data, result, f"image_{i+1}.jpg")
                print(f"  💾 Result cached")
            
            results.append(result)
        
        # Verify results
        stats = cache.get_stats()
        print(f"\n📊 Integration Test Results:")
        print(f"  Total images processed: {len(test_images)}")
        print(f"  OCR service calls: {ocr_service.call_count}")
        print(f"  Cache hits: {stats.hits}")
        print(f"  Cache misses: {stats.misses}")
        print(f"  Hit ratio: {stats.hit_ratio:.1f}%")
        print(f"  Performance improvement: {((len(test_images) - ocr_service.call_count) / len(test_images)) * 100:.1f}%")
        
        # Cleanup
        cache.close()
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("✅ Integration test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


if __name__ == '__main__':
    if not MODULES_AVAILABLE:
        print("❌ Cannot run tests - required modules not available")
        exit(1)
    
    print("🧪 Running OCR Cache Test Suite")
    print("=" * 50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    run_integration_test()
