2025-09-19 09:23:49,315 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=12, memory=15.8GB, available=6.9GB, cpu_usage=5.4%, optimal_threads=3
2025-09-19 11:34:45,055 - EmailProcessor.Threading - INFO - Threading approved: files=2810, memory=6.9GB available, total_memory=15.8GB
2025-09-19 11:34:54,011 - EmailProcessor.Threading - INFO - Threading approved: files=2810, memory=6.9GB available, total_memory=15.8GB
2025-09-19 11:34:54,119 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=2810, memory=15.8GB, available=6.9GB, cpu_usage=8.2%, optimal_threads=3
2025-09-22 09:41:56,054 - EmailProcessor.Threading - INFO - Threading approved: files=579, memory=6.5GB available, total_memory=15.8GB
2025-09-22 09:42:00,018 - EmailProcessor.Threading - INFO - Threading approved: files=579, memory=6.5GB available, total_memory=15.8GB
2025-09-22 09:42:00,135 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=579, memory=15.8GB, available=6.5GB, cpu_usage=35.7%, optimal_threads=3
2025-09-22 11:15:01,059 - EmailProcessor.Threading - INFO - Threading approved: files=1900, memory=5.3GB available, total_memory=15.8GB
2025-09-22 11:15:02,340 - EmailProcessor.Threading - INFO - Threading approved: files=1900, memory=5.3GB available, total_memory=15.8GB
2025-09-22 11:15:02,448 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=1900, memory=15.8GB, available=5.3GB, cpu_usage=5.4%, optimal_threads=3
2025-09-22 14:59:33,789 - EmailProcessor.Threading - INFO - Threading approved: files=20, memory=10.4GB available, total_memory=15.8GB
2025-09-22 14:59:41,968 - EmailProcessor.Threading - INFO - Threading approved: files=20, memory=10.4GB available, total_memory=15.8GB
2025-09-22 14:59:42,076 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=20, memory=15.8GB, available=10.4GB, cpu_usage=23.2%, optimal_threads=3
2025-09-22 15:45:43,678 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:22,028 - EmailProcessor.Threading - INFO - Threading approved: files=10, memory=7.5GB available, total_memory=15.8GB
2025-09-22 15:46:28,967 - EmailProcessor.Threading - INFO - Threading approved: files=10, memory=7.5GB available, total_memory=15.8GB
2025-09-22 15:46:29,075 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=10, memory=15.8GB, available=7.5GB, cpu_usage=5.3%, optimal_threads=3
2025-09-22 15:46:29,099 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,100 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,101 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,107 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,108 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,108 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:29,375 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:46:30,733 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 15:51:39,761 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:01,793 - EmailProcessor.Threading - INFO - Threading approved: files=10, memory=8.0GB available, total_memory=15.8GB
2025-09-22 16:12:03,090 - EmailProcessor.Threading - INFO - Threading approved: files=10, memory=8.0GB available, total_memory=15.8GB
2025-09-22 16:12:03,200 - EmailProcessor.Threading - INFO - Dynamic thread calculation: files=10, memory=15.8GB, available=8.0GB, cpu_usage=8.6%, optimal_threads=3
2025-09-22 16:12:03,222 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,224 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,224 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,229 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,293 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,293 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:03,754 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:04,266 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:05,088 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:05,180 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:05,838 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:05,960 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:05,965 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
2025-09-22 16:12:07,859 - ocr_cache - INFO - OCR cache database initialized at .cache\ocr_cache.db
