#!/usr/bin/env python3
"""
OCR Cache Demonstration Script
Shows the performance benefits of OCR result caching
"""

import time
import hashlib
from pathlib import Path

# Import required modules
try:
    from paddle_ocr_service import PaddleOcrService
    from config import initialize_config, get_cache_config
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ Error: Required modules not available: {e}")
    MODULES_AVAILABLE = False


def create_sample_image_data():
    """Create sample image data for demonstration"""
    # This would normally be actual image bytes
    sample_images = [
        b"Sample image data 1 - this represents a PNG/JPEG file",
        b"Sample image data 2 - another image file",
        b"Sample image data 1 - this represents a PNG/JPEG file",  # Duplicate
        b"Sample image data 3 - third unique image",
        b"Sample image data 2 - another image file",  # Duplicate
        b"Sample image data 4 - fourth unique image",
    ]
    return sample_images


def simulate_ocr_processing(image_data):
    """Simulate OCR processing (normally done by PaddleOCR)"""
    # Simulate processing time
    time.sleep(0.5)  # Simulate 500ms OCR processing time
    
    # Generate a "result" based on the image data
    hash_part = hashlib.sha256(image_data).hexdigest()[:8]
    return f"OCR extracted text from image (hash: {hash_part})"


def demo_without_cache():
    """Demonstrate OCR processing without caching"""
    print("\n🔄 Demo: OCR Processing WITHOUT Caching")
    print("=" * 50)
    
    sample_images = create_sample_image_data()
    results = []
    
    start_time = time.time()
    
    for i, image_data in enumerate(sample_images, 1):
        print(f"Processing image {i}...")
        result = simulate_ocr_processing(image_data)
        results.append(result)
        print(f"  Result: {result[:50]}...")
    
    total_time = time.time() - start_time
    
    print(f"\n📊 Results WITHOUT Caching:")
    print(f"  Total images processed: {len(sample_images)}")
    print(f"  Total processing time: {total_time:.2f} seconds")
    print(f"  Average time per image: {total_time/len(sample_images):.2f} seconds")
    
    return results, total_time


def demo_with_cache():
    """Demonstrate OCR processing with caching"""
    print("\n⚡ Demo: OCR Processing WITH Caching")
    print("=" * 50)
    
    # Initialize cache
    try:
        from ocr_cache import initialize_cache
        cache = initialize_cache(
            cache_dir=".demo_cache",
            cache_enabled=True,
            expiration_days=1
        )
        print("✅ Cache system initialized")
    except Exception as e:
        print(f"❌ Failed to initialize cache: {e}")
        return [], 0
    
    sample_images = create_sample_image_data()
    results = []
    cache_hits = 0
    cache_misses = 0
    
    start_time = time.time()
    
    for i, image_data in enumerate(sample_images, 1):
        print(f"Processing image {i}...")
        
        # Check cache first
        cached_result = cache.lookup(image_data, f"image_{i}.jpg")
        
        if cached_result:
            print(f"  ✅ Cache hit - using cached result")
            result = cached_result
            cache_hits += 1
        else:
            print(f"  🔄 Cache miss - performing OCR")
            result = simulate_ocr_processing(image_data)
            cache.store(image_data, result, f"image_{i}.jpg")
            print(f"  💾 Result cached for future use")
            cache_misses += 1
        
        results.append(result)
        print(f"  Result: {result[:50]}...")
    
    total_time = time.time() - start_time
    
    # Get cache statistics
    stats = cache.get_stats()
    
    print(f"\n📊 Results WITH Caching:")
    print(f"  Total images processed: {len(sample_images)}")
    print(f"  Cache hits: {cache_hits}")
    print(f"  Cache misses: {cache_misses}")
    print(f"  Hit ratio: {(cache_hits / len(sample_images)) * 100:.1f}%")
    print(f"  Total processing time: {total_time:.2f} seconds")
    print(f"  Average time per image: {total_time/len(sample_images):.2f} seconds")
    
    return results, total_time


def demo_performance_comparison():
    """Compare performance with and without caching"""
    print("\n🏁 Performance Comparison")
    print("=" * 50)
    
    # Run without cache
    _, time_without_cache = demo_without_cache()
    
    # Run with cache
    _, time_with_cache = demo_with_cache()
    
    # Calculate improvement
    if time_without_cache > 0:
        improvement = ((time_without_cache - time_with_cache) / time_without_cache) * 100
        speedup = time_without_cache / time_with_cache if time_with_cache > 0 else float('inf')
        
        print(f"\n🚀 Performance Improvement:")
        print(f"  Time without cache: {time_without_cache:.2f} seconds")
        print(f"  Time with cache: {time_with_cache:.2f} seconds")
        print(f"  Time saved: {time_without_cache - time_with_cache:.2f} seconds")
        print(f"  Performance improvement: {improvement:.1f}%")
        print(f"  Speedup factor: {speedup:.1f}x")
        
        if improvement > 0:
            print("✅ Caching provides significant performance benefits!")
        else:
            print("ℹ️ Caching overhead detected (normal for small datasets)")


def demo_cache_management():
    """Demonstrate cache management features"""
    print("\n🛠️ Cache Management Demo")
    print("=" * 50)
    
    try:
        from ocr_cache import get_cache
        cache = get_cache()
        
        if not cache:
            print("❌ Cache not available")
            return
        
        # Show cache info
        info = cache.get_cache_info()
        print("📋 Cache Information:")
        for key, value in info.items():
            if key == 'statistics':
                print(f"  {key}:")
                for stat_key, stat_value in value.items():
                    print(f"    {stat_key}: {stat_value}")
            else:
                print(f"  {key}: {value}")
        
        # Demonstrate cleanup
        print("\n🧹 Testing cache cleanup...")
        removed = cache.cleanup_expired()
        print(f"  Removed {removed} expired entries")
        
    except Exception as e:
        print(f"❌ Cache management demo failed: {e}")


def main():
    """Main demonstration function"""
    if not MODULES_AVAILABLE:
        print("❌ Cannot run demo - required modules not available")
        return
    
    print("🎯 OCR Cache System Demonstration")
    print("=" * 50)
    print("This demo shows how OCR result caching improves performance")
    print("by avoiding redundant OCR processing for identical files.")
    print()
    
    try:
        # Initialize configuration
        initialize_config()
        
        # Run performance comparison
        demo_performance_comparison()
        
        # Show cache management features
        demo_cache_management()
        
        print("\n✅ Demo completed successfully!")
        print("\nTo see the cache in action with real OCR:")
        print("1. Run the email processor with OCR-enabled files")
        print("2. Process the same files again to see cache hits")
        print("3. Use 'python cache_manager.py stats' to view statistics")
        
    except KeyboardInterrupt:
        print("\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
    finally:
        # Cleanup demo cache
        try:
            import shutil
            demo_cache_path = Path(".demo_cache")
            if demo_cache_path.exists():
                shutil.rmtree(demo_cache_path)
                print("🗑️ Demo cache cleaned up")
        except Exception:
            pass


if __name__ == '__main__':
    main()
